#!/usr/bin/env python3
"""
测试 JWT 功能的脚本
"""

import jwt
from datetime import datetime, timezone, timedelta

def test_jwt_basic():
    """测试基本的 JWT 编码和解码功能"""
    print("=== 测试基本 JWT 功能 ===")
    
    # 测试数据
    secret_key = "test-secret-key-123456"
    algorithm = "HS256"
    
    # 创建测试负载
    payload = {
        "user_id": 1,
        "username": "test_user",
        "is_superuser": False,
        "exp": datetime.now(timezone.utc) + timedelta(hours=1)
    }
    
    try:
        # 测试编码
        print("1. 测试 JWT 编码...")
        token = jwt.encode(payload, secret_key, algorithm=algorithm)
        print(f"✓ JWT Token 创建成功: {token[:50]}...")
        
        # 测试解码
        print("2. 测试 JWT 解码...")
        decoded_payload = jwt.decode(token, secret_key, algorithms=[algorithm])
        print(f"✓ JWT Token 解码成功: user_id={decoded_payload.get('user_id')}, username={decoded_payload.get('username')}")
        
        return True
        
    except Exception as e:
        print(f"✗ JWT 测试失败: {e}")
        return False

def test_jwt_with_app_config():
    """使用应用配置测试 JWT"""
    print("\n=== 测试应用配置的 JWT 功能 ===")
    
    try:
        # 模拟应用配置
        class MockSettings:
            SECRET_KEY = "3488a63e1765035d386f05409663f55c83bfae3b3c61a932744b20ad14244dcf"
            JWT_ALGORITHM = "HS256"
            JWT_ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 7
        
        settings = MockSettings()
        
        # 模拟 JWTPayload
        class MockJWTPayload:
            def __init__(self, user_id, username, is_superuser, exp):
                self.user_id = user_id
                self.username = username
                self.is_superuser = is_superuser
                self.exp = exp
            
            def model_dump(self):
                return {
                    "user_id": self.user_id,
                    "username": self.username,
                    "is_superuser": self.is_superuser,
                    "exp": self.exp
                }
        
        # 创建测试负载
        test_payload = MockJWTPayload(
            user_id=1,
            username="test_user",
            is_superuser=False,
            exp=datetime.now(timezone.utc) + timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        )
        
        # 模拟 create_access_token 函数
        def create_access_token(data):
            payload = data.model_dump().copy()
            encoded_jwt = jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
            return encoded_jwt
        
        # 测试创建 token
        print("1. 测试创建访问令牌...")
        token = create_access_token(test_payload)
        print(f"✓ 访问令牌创建成功: {token[:50]}...")
        
        # 测试解码 token
        print("2. 测试解码访问令牌...")
        decoded_data = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        print(f"✓ 访问令牌解码成功: user_id={decoded_data.get('user_id')}, username={decoded_data.get('username')}")
        
        return True
        
    except Exception as e:
        print(f"✗ 应用配置 JWT 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试 JWT 功能...\n")
    
    # 运行测试
    test1_result = test_jwt_basic()
    test2_result = test_jwt_with_app_config()
    
    # 总结
    print(f"\n=== 测试结果总结 ===")
    print(f"基本 JWT 功能: {'✓ 通过' if test1_result else '✗ 失败'}")
    print(f"应用配置 JWT 功能: {'✓ 通过' if test2_result else '✗ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有 JWT 测试都通过了！您的 JWT 问题已经解决。")
    else:
        print("\n❌ 部分测试失败，请检查错误信息。")
