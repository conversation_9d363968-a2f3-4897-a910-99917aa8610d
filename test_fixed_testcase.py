#!/usr/bin/env python3
"""
测试修复后的测试用例 Schema
"""

import sys
import os
from typing import Optional
from enum import Enum
from pydantic import BaseModel, Field, ValidationError

# 直接定义枚举和 Schema
class Priority(str, Enum):
    HIGH = "高"
    MEDIUM = "中"
    LOW = "低"

class Status(str, Enum):
    NOT_STARTED = "未开始"
    IN_PROGRESS = "进行中"
    PASSED = "通过"
    FAILED = "失败"
    BLOCKED = "阻塞"

class TestCaseTag(str, Enum):
    UNIT_TEST = "单元测试"
    FUNCTIONAL_TEST = "功能测试"
    INTEGRATION_TEST = "集成测试"
    SYSTEM_TEST = "系统测试"
    SMOKE_TEST = "冒烟测试"
    VERSION_VERIFICATION = "版本验证"

# 测试步骤模型
class TestStepBase(BaseModel):
    description: str = Field(..., min_length=1, description="测试步骤的描述。")
    expected_result: str = Field(..., min_length=1, description="测试步骤的预期结果。")

class TestStepCreate(TestStepBase):
    pass

# 测试用例模型
class TestCaseBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200, description="测试用例的标题。")
    desc: Optional[str] = Field(None, max_length=1000, description="测试用例的详细描述。")
    priority: Priority = Field(..., description="测试用例的优先级。")
    status: Status = Field(default=Status.NOT_STARTED, description="测试用例的当前状态。")
    preconditions: Optional[str] = Field(None, description="测试用例的前置条件。")
    postconditions: Optional[str] = Field(None, description="测试用例的后置条件。")
    tags: Optional[TestCaseTag] = Field(TestCaseTag.FUNCTIONAL_TEST, description="测试用例的标签，用于分类或过滤。")
    requirement_id: int = Field(..., gt=0, description="关联需求ID。")
    project_id: int = Field(..., gt=0, description="关联项目ID。")
    creator: str = Field(..., min_length=1, max_length=100, description="测试用例的创建者姓名。")

class CaseCreate(TestCaseBase):
    steps: Optional[list[TestStepCreate]] = Field(None, description="测试步骤列表。")

def test_user_error_case():
    """测试用户提供的错误数据"""
    print("=== 测试用户提供的错误数据 ===")
    
    # 用户提供的数据（这是测试步骤数据，不是测试用例数据）
    user_data = {
        'description': '第一步',
        'expected_result': '无'
    }
    
    print("1. 测试用户数据作为测试用例...")
    try:
        testcase = CaseCreate(**user_data)
        print("✗ 应该验证失败但却成功了")
        return False
    except ValidationError as e:
        print("✓ 正确拒绝了错误的测试用例数据:")
        for error in e.errors():
            print(f"     - {error['loc'][0]}: {error['msg']}")
    
    print("\n2. 测试用户数据作为测试步骤...")
    try:
        step = TestStepCreate(**user_data)
        print("✓ 作为测试步骤验证通过")
        print(f"   描述: {step.description}")
        print(f"   预期结果: {step.expected_result}")
    except ValidationError as e:
        print(f"✗ 作为测试步骤验证失败: {e}")
        return False
    
    return True

def test_valid_testcase():
    """测试有效的测试用例数据"""
    print("\n=== 测试有效的测试用例数据 ===")
    
    # 最小有效数据
    print("1. 测试最小有效数据...")
    minimal_data = {
        "title": "登录功能测试",
        "priority": "高",
        "requirement_id": 1,
        "project_id": 1,
        "creator": "测试工程师"
    }
    
    try:
        testcase = CaseCreate(**minimal_data)
        print("✓ 最小有效数据验证通过")
        print(f"   标题: {testcase.title}")
        print(f"   优先级: {testcase.priority}")
        print(f"   状态: {testcase.status}")
        print(f"   标签: {testcase.tags}")
        print(f"   需求ID: {testcase.requirement_id}")
        print(f"   项目ID: {testcase.project_id}")
        print(f"   创建者: {testcase.creator}")
    except ValidationError as e:
        print(f"✗ 最小有效数据验证失败:")
        for error in e.errors():
            print(f"     - {error['loc'][0]}: {error['msg']}")
        return False
    
    # 完整有效数据
    print("\n2. 测试完整有效数据...")
    full_data = {
        "title": "用户登录完整测试",
        "desc": "测试用户登录功能的各种场景",
        "priority": "高",
        "status": "未开始",
        "preconditions": "用户已注册",
        "postconditions": "用户成功登录",
        "tags": "功能测试",
        "requirement_id": 1,
        "project_id": 1,
        "creator": "张三",
        "steps": [
            {
                "description": "输入正确的用户名和密码",
                "expected_result": "登录成功，跳转到主页"
            },
            {
                "description": "输入错误的密码",
                "expected_result": "显示密码错误提示"
            }
        ]
    }
    
    try:
        testcase = CaseCreate(**full_data)
        print("✓ 完整有效数据验证通过")
        print(f"   包含 {len(testcase.steps or [])} 个测试步骤")
        print(f"   标签: {testcase.tags}")
    except ValidationError as e:
        print(f"✗ 完整有效数据验证失败:")
        for error in e.errors():
            print(f"     - {error['loc'][0]}: {error['msg']}")
        return False
    
    return True

def test_invalid_data():
    """测试无效数据"""
    print("\n=== 测试无效数据验证 ===")
    
    base_data = {
        "title": "测试用例",
        "priority": "高",
        "requirement_id": 1,
        "project_id": 1,
        "creator": "测试人员"
    }
    
    invalid_cases = [
        {
            "name": "空标题",
            "data": {**base_data, "title": ""},
            "should_fail": True
        },
        {
            "name": "空创建者",
            "data": {**base_data, "creator": ""},
            "should_fail": True
        },
        {
            "name": "负数需求ID",
            "data": {**base_data, "requirement_id": -1},
            "should_fail": True
        },
        {
            "name": "零需求ID",
            "data": {**base_data, "requirement_id": 0},
            "should_fail": True
        },
        {
            "name": "负数项目ID",
            "data": {**base_data, "project_id": -1},
            "should_fail": True
        },
        {
            "name": "零项目ID",
            "data": {**base_data, "project_id": 0},
            "should_fail": True
        },
        {
            "name": "无效优先级",
            "data": {**base_data, "priority": "无效"},
            "should_fail": True
        },
        {
            "name": "无效标签",
            "data": {**base_data, "tags": "无效标签"},
            "should_fail": True
        }
    ]
    
    failed_count = 0
    for case in invalid_cases:
        try:
            CaseCreate(**case["data"])
            if case["should_fail"]:
                print(f"✗ {case['name']}: 应该验证失败但却成功了")
                failed_count += 1
            else:
                print(f"✓ {case['name']}: 验证通过")
        except ValidationError:
            if case["should_fail"]:
                print(f"✓ {case['name']}: 正确捕获验证错误")
            else:
                print(f"✗ {case['name']}: 不应该验证失败")
                failed_count += 1
    
    return failed_count == 0

def main():
    """主测试函数"""
    print("开始测试修复后的测试用例 Schema...\n")
    
    tests = [
        ("用户错误数据", test_user_error_case),
        ("有效测试用例", test_valid_testcase),
        ("无效数据验证", test_invalid_data),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print(f"\n{'='*60}")
    print("测试结果总结:")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 测试用例 Schema 修复成功！")
        print("\n📝 使用说明:")
        print("- 创建测试用例时，必须提供: title, priority, requirement_id, project_id, creator")
        print("- 测试步骤是可选的，可以在 steps 字段中提供")
        print("- 每个测试步骤必须包含: description, expected_result")
        return True
    else:
        print(f"\n❌ 还有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
