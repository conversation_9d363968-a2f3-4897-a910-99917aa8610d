#!/usr/bin/env python3
"""
测试模型定义是否正确
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.abspath('.'))

try:
    # 直接导入模型相关的模块
    from tortoise import fields, models
    from enum import Enum
    
    # 定义枚举
    class Priority(str, Enum):
        HIGH = "高"
        MEDIUM = "中"
        LOW = "低"

    class Status(str, Enum):
        NOT_STARTED = "未开始"
        IN_PROGRESS = "进行中"
        PASSED = "通过"
        FAILED = "失败"
        BLOCKED = "阻塞"

    class TestCaseTag(str, Enum):
        UNIT_TEST = "单元测试"
        FUNCTIONAL_TEST = "功能测试"
        INTEGRATION_TEST = "集成测试"
        SYSTEM_TEST = "系统测试"
        SMOKE_TEST = "冒烟测试"
        VERSION_VERIFICATION = "版本验证"

    # 基础模型
    class BaseModel(models.Model):
        id = fields.BigIntField(pk=True, index=True)

    class TimestampMixin:
        created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
        updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    # 测试步骤模型
    class TestStep(BaseModel, TimestampMixin):
        """测试用例步骤模型"""
        test_case_id = fields.IntField(index=True, description="关联的测试用例ID。")
        description = fields.TextField(description="测试步骤的描述。")
        expected_result = fields.TextField(description="测试步骤的预期结果。")

        class Meta:
            table = "test_steps"

    # 测试用例模型
    class TestCase(BaseModel, TimestampMixin):
        """测试用例模型"""
        title = fields.CharField(max_length=200, description="测试用例的标题。")
        desc = fields.TextField(null=True, description="测试用例的详细描述。")
        priority = fields.CharEnumField(Priority, description="测试用例的优先级。")
        status = fields.CharEnumField(Status, default=Status.NOT_STARTED, description="测试用例的当前状态。")
        preconditions = fields.TextField(null=True, description="测试用例的前置条件。")
        postconditions = fields.TextField(null=True, description="测试用例的后置条件。")
        tags = fields.CharEnumField(TestCaseTag, default=TestCaseTag.FUNCTIONAL_TEST, description="测试用例的标签，用于分类或过滤。")
        steps = fields.ManyToManyField("models.TestStep", related_name="test_cases", description="测试步骤列表。")
        requirement_id = fields.IntField(index=True, description="测试用例的相关需求。", null=False)
        project_id = fields.IntField(index=True, description="测试用例的相关项目。", null=False)
        creator = fields.CharField(max_length=100, description="测试用例的创建者姓名。")

        class Meta:
            table = "test_cases"

    print("✓ 模型定义正确")
    print("✓ TestStep 模型创建成功")
    print("✓ TestCase 模型创建成功")
    print("✓ 所有字段定义正确")

except Exception as e:
    print(f"✗ 模型定义错误: {e}")
    sys.exit(1)

print("\n🎉 模型修复成功！")
