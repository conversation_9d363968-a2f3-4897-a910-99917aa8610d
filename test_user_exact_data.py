#!/usr/bin/env python3
"""
使用用户提供的确切数据测试修复
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.abspath('.'))

try:
    from app.schemas.testcases import CaseCreate, TestCase
    from pydantic import ValidationError
    
    print("=== 使用用户提供的确切数据测试 ===")
    
    # 用户报告的错误数据（包含 id 字段）
    user_data_with_id = {
        'title': '第一个用例',
        'desc': '这是第一个测试用例',
        'priority': '高',
        'requirement_id': 1,
        'project_id': 1,
        'creator': '测试工程师',
        'steps': [
            {
                'description': '步骤1',
                'expected_result': '成功',
                'id': 1  # 这个不应该在创建时提供
            }
        ],
        'id': 1  # 这个也不应该在创建时提供
    }
    
    print("1. 测试包含 id 字段的数据（应该失败）...")
    try:
        testcase = CaseCreate(**user_data_with_id)
        print("✗ 错误：包含 id 字段的数据应该被拒绝")
    except ValidationError as e:
        print("✓ 正确拒绝了包含 id 字段的数据")
        for error in e.errors():
            print(f"     - {error['loc']}: {error['msg']}")
    
    # 正确的数据（移除 id 字段）
    user_data_correct = {
        'title': '第一个用例',
        'desc': '这是第一个测试用例',
        'priority': '高',
        'requirement_id': 1,
        'project_id': 1,
        'creator': '测试工程师',
        'steps': [
            {
                'description': '步骤1',
                'expected_result': '成功'
                # 移除了 id
            }
        ]
        # 移除了 id
    }
    
    print("\n2. 测试移除 id 字段后的数据（应该成功）...")
    try:
        testcase = CaseCreate(**user_data_correct)
        print("✓ 正确的数据验证通过")
        print(f"   标题: {testcase.title}")
        print(f"   描述: {testcase.desc}")
        print(f"   优先级: {testcase.priority}")
        print(f"   需求ID: {testcase.requirement_id}")
        print(f"   项目ID: {testcase.project_id}")
        print(f"   创建者: {testcase.creator}")
        print(f"   步骤数量: {len(testcase.steps or [])}")
        if testcase.steps:
            for i, step in enumerate(testcase.steps, 1):
                print(f"   步骤{i}: {step.description} -> {step.expected_result}")
    except ValidationError as e:
        print("✗ 正确的数据验证失败:")
        for error in e.errors():
            print(f"     - {error['loc']}: {error['msg']}")
    
    print("\n🎉 修复验证成功！")
    print("\n📝 用户现在应该:")
    print("- ✅ 移除创建请求中的所有 id 字段")
    print("- ✅ 只在测试步骤中提供 description 和 expected_result")
    print("- ✅ 只在测试用例中提供必需的字段（title, priority, requirement_id, project_id, creator）")
    print("- ✅ id 字段会在数据库中自动生成")

except ImportError as e:
    print(f"✗ 导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
except Exception as e:
    print(f"✗ 测试异常: {e}")
