html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

html {
  font-size: 4px; // * 1rem = 4px  方便unocss计算：在unocss中 1字体单位 = 0.25rem，相当于 1等份 = 1px
}

body {
  font-size: 16px;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  color: #1E293B;
  transition: background-color 0.3s ease;
  position: relative;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      linear-gradient(rgba(109, 40, 217, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(109, 40, 217, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 0;
  }
}

#app {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

/* transition fade-slide */
.fade-slide-leave-active,
.fade-slide-enter-active {
  transition: all 0.3s;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* 自定义滚动条样式 */
.cus-scroll {
  overflow: auto;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
}
.cus-scroll-x {
  overflow-x: auto;
  &::-webkit-scrollbar {
    width: 0;
    height: 6px;
  }
}
.cus-scroll-y {
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 6px;
    height: 0;
  }
}
.cus-scroll,
.cus-scroll-x,
.cus-scroll-y {
  &::-webkit-scrollbar-thumb {
    background-color: rgba(109, 40, 217, 0.2);
    border-radius: 3px;
  }
  &:hover {
    &::-webkit-scrollbar-thumb {
      background: rgba(109, 40, 217, 0.4);
    }
    &::-webkit-scrollbar-thumb:hover {
      background: var(--primary-color);
    }
  }
}

/* 卡片样式 */
.n-card {
  background: #FFFFFF !important;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(109, 40, 217, 0.08);
  box-shadow: 0 4px 20px rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;

  .n-card__content {
    background: #F8FAFC !important;
    border-radius: 0 0 12px 12px;
    padding: 20px !important;
  }

  .n-card-header {
    padding: 20px !important;
    background: #FFFFFF !important;
    border-bottom: 1px solid rgba(109, 40, 217, 0.08);
    color: #1E293B !important;
    font-weight: 600;
    border-radius: 12px 12px 0 0;
  }

  &:hover {
    border-color: rgba(109, 40, 217, 0.2);
    box-shadow: 0 8px 30px rgba(148, 163, 184, 0.15);
  }
}

/* 表格样式 */
.n-data-table {
  background: #FFFFFF !important;
  border: 1px solid rgba(109, 40, 217, 0.08);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);

  .n-data-table-thead {
    background: #F8FAFC !important;
    
    th {
      color: #475569 !important;
      font-weight: 600;
      font-size: 14px;
      padding: 16px !important;
      border-bottom: 1px solid rgba(109, 40, 217, 0.12) !important;
      background: #F8FAFC !important;
      transition: background-color 0.3s ease;

      &:hover {
        background: #F1F5F9 !important;
      }
    }
  }

  .n-data-table-tr {
    td {
      padding: 14px 16px !important;
      color: #1E293B !important;
      font-size: 14px;
      border-bottom: 1px solid rgba(109, 40, 217, 0.06) !important;
      background: #FFFFFF;
      transition: all 0.3s ease;
    }
    
    &:hover {
      td {
        background: rgba(109, 40, 217, 0.02) !important;
      }
    }

    &:last-child {
      td {
        border-bottom: none !important;
      }
    }
  }

  // 选中行样式
  .n-data-table-tr.n-data-table-tr--checked {
    td {
      background: rgba(109, 40, 217, 0.04) !important;
    }
  }

  // 表格内链接样式
  .n-data-table-td__ellipsis {
    color: #6D28D9 !important;
    
    &:hover {
      color: #7C3AED !important;
      text-decoration: underline;
    }
  }

  // 分页器样式优化
  .n-pagination {
    margin-top: 16px;
    padding: 12px 16px;
    background: #F8FAFC;
    border-top: 1px solid rgba(109, 40, 217, 0.08);

    .n-pagination-item {
      background: transparent;
      color: #475569;
      border: 1px solid rgba(109, 40, 217, 0.1);
      
      &:hover {
        color: #6D28D9;
        border-color: #6D28D9;
      }

      &.n-pagination-item--active {
        background: #6D28D9;
        color: white;
        border-color: #6D28D9;
      }
    }
  }

  // 空状态样式
  .n-data-table-empty {
    padding: 32px;
    color: #94A3B8;
    font-size: 14px;
  }

  // 加载状态样式
  .n-data-table-loading {
    background: rgba(255, 255, 255, 0.8);
    
    .n-spin-body {
      color: #6D28D9;
    }
  }
}

// 表格内的操作按钮组
.table-action-buttons {
  display: flex;
  gap: 8px;
  
  .n-button {
    padding: 4px 12px;
    font-size: 13px;
    height: 28px;
    
    &:hover {
      transform: translateY(-1px);
    }
  }
}

/* 按钮样式 */
.n-button {
  background: linear-gradient(45deg, var(--primary-color) 0%, var(--info-color) 100%);
  border: none;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
  color: white !important;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(109, 40, 217, 0.3);
  }
}

/* 输入框样式 */
.n-input {
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(109, 40, 217, 0.1);
  backdrop-filter: blur(5px);

  &:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(109, 40, 217, 0.2);
  }
}

html, body, #app {
  height: 100%;
  margin: 0;
  overflow-x: hidden;
  position: relative;
  z-index: 1;
}

.n-card-header {
  padding: 20px 40px 0 40px !important;
  background: rgba(248, 250, 252, 0.9) !important;
  border-bottom: 1px solid rgba(109, 40, 217, 0.1);
  color: #1E293B !important;
}