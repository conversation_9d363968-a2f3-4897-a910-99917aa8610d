<!doctype html>
<html lang="cn">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-control" content="no-cache" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" href="/favicon.svg" />
    <link rel="stylesheet" href="/resource/loading.css" />

    <title><%= title %></title>
    <!-- 添加Monaco编辑器CDN -->
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs/loader.js"></script>
    <script>
      require.config({
        paths: {
          'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs'
        }
      });
      window.MonacoEnvironment = {
        getWorkerUrl: function (workerId, label) {
          return `data:text/javascript;charset=utf-8,${encodeURIComponent(`
            self.MonacoEnvironment = {
              baseUrl: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/'
            };
            importScripts('https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs/base/worker/workerMain.js');
          `)}`;
        }
      };
    </script>
    <!-- 预加载字体 -->
    <link rel="preload" href="/fonts/SmileySans-Oblique.ttf" as="font" type="font/ttf" crossorigin />
  </head>

  <body>
    <div id="app">
      <!-- 白屏时的loading效果 -->
      <div class="loading-container">
        <div id="loadingLogo" class="loading-svg"></div>
        <div class="loading-spin__container">
          <div class="loading-spin">
            <div class="left-0 top-0 loading-spin-item"></div>
            <div class="left-0 bottom-0 loading-spin-item loading-delay-500"></div>
            <div class="right-0 top-0 loading-spin-item loading-delay-1000"></div>
            <div class="right-0 bottom-0 loading-spin-item loading-delay-1500"></div>
          </div>
        </div>
        <div class="loading-title"><%= title %></div>
      </div>
      <script src="/resource/loading.js"></script>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
