aiosqlite==0.17.0
annotated-types==0.6.0
anyio==4.4.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
black==23.12.1
certifi==2024.7.4
cffi~=1.17.1
click==8.1.7
dictdiffer==0.9.0
dnspython==2.6.1
email_validator==2.2.0
fastapi~=0.115.11
fastapi-cli==0.0.5
h11==0.14.0
httpcore==1.0.5
httptools==0.6.3
httpx~=0.28.1
idna==3.8
#iso8601==1.1.0(原)
#iso8601>=2.1.0,<3.0.0
isort==5.13.2
Jinja2~=3.1.6
loguru==0.7.2
markdown-it-py==3.0.0
MarkupSafe==2.1.5
mdurl==0.1.2
mypy-extensions==1.0.0
orjson==3.10.7
packaging~=24.2
passlib==1.7.4
pathspec==0.12.1
platformdirs==4.2.2
pycparser==2.22
pydantic~=2.10.6
pydantic-settings==2.4.0
#pydantic_core==2.23.0(原)
pydantic_core==2.27.2
Pygments~=2.19.1
PyJWT==2.10.1
pypika-tortoise==0.5.0
python-dotenv==1.0.1
python-multipart==0.0.9
pytz~=2025.1
PyYAML==6.0.2
rich==13.8.0
ruff==0.0.281
setuptools==70.3.0
shellingham==1.5.4
sniffio==1.3.1
starlette~=0.46.0
tomlkit==0.13.2
tortoise-orm==0.24.2
typer==0.12.5
typing_extensions==4.12.2
tzdata==2024.1
ujson==5.10.0
uvicorn~=0.34.3
watchfiles==0.23.0
websockets==13.0
aerich==0.8.2
autogen-agentchat==*******
autogen-ext[openai]==*******
autogen-core==*******
pytest==8.3.5
allure-pytest==2.13.2
magentic==0.12.0


enum34~=1.1.10
requests~=2.32.3
#allure-python-commons~=2.13.5(原)
allure-python-commons~=2.13.2
mimesis~=18.0.0
PyMySQL~=1.1.1
defusedxml~=0.7.1
numpy~=2.2.3
pillow~=11.1.0
olefile~=0.47
pip~=23.2.1
attrs~=25.1.0
distro~=1.9.0
wheel~=0.41.2
protobuf~=5.29.3
cryptography~=44.0.2
filelock~=3.17.0
charset-normalizer~=3.4.1
overrides~=7.7.0
wcwidth~=0.2.13
pluggy~=1.5.0
Deprecated~=1.2.18
iniconfig~=2.0.0
argcomplete~=3.6.0
six~=1.17.0
python-dateutil~=2.9.0.post0
matplotlib~=3.10.1
contourpy~=1.3.1
fonttools~=4.56.0
lxml~=5.3.1
sympy~=1.13.1
scipy~=1.15.2
pyparsing~=3.2.1
cycler~=0.12.1
kiwisolver~=1.4.8
build~=1.2.2.post1
docling~=2.25.2
llama-index-core~=0.12.22
aiofiles~=24.1.0
openai~=1.65.4

nest-asyncio~=1.6.0
DBUtils~=3.1.0
pydantic_ai