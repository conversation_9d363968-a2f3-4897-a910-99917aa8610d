#!/usr/bin/env python3
"""
测试测试用例 Schema 验证
"""

import sys
import os
from typing import List, Optional
from enum import Enum
from pydantic import BaseModel, Field, ValidationError

# 直接定义枚举和 Schema，避免导入问题
class Priority(str, Enum):
    HIGH = "高"
    MEDIUM = "中"
    LOW = "低"

class Status(str, Enum):
    NOT_STARTED = "未开始"
    IN_PROGRESS = "进行中"
    PASSED = "通过"
    FAILED = "失败"
    BLOCKED = "阻塞"

class TestCaseTag(str, Enum):
    UNIT_TEST = "单元测试"
    FUNCTIONAL_TEST = "功能测试"
    INTEGRATION_TEST = "集成测试"
    SYSTEM_TEST = "系统测试"
    SMOKE_TEST = "冒烟测试"
    VERSION_VERIFICATION = "版本验证"

# 测试步骤模型
class TestStepBase(BaseModel):
    description: str = Field(..., description="测试步骤的描述。")
    expected_result: str = Field(..., description="测试步骤的预期结果。")

class TestStepCreate(TestStepBase):
    pass

# 测试用例模型
class TestCaseBase(BaseModel):
    title: str = Field(..., max_length=200, description="测试用例的标题。")
    desc: Optional[str] = Field(None, max_length=1000, description="测试用例的详细描述。")
    priority: Priority = Field(..., description="测试用例的优先级。")
    status: Status = Field(default=Status.NOT_STARTED, description="测试用例的当前状态。")
    preconditions: Optional[str] = Field(None, description="测试用例的前置条件。")
    postconditions: Optional[str] = Field(None, description="测试用例的后置条件。")
    tags: Optional[List[TestCaseTag]] = Field(None, description="测试用例的标签列表，用于分类或过滤。")
    requirement_id: int = Field(..., description="关联需求ID。")
    project_id: int = Field(..., description="关联项目ID。")
    creator: str = Field(..., max_length=100, description="测试用例的创建者姓名。")

class CaseCreate(TestCaseBase):
    steps: Optional[List[TestStepCreate]] = Field(None, description="测试步骤列表。")

def test_testcase_create_validation():
    """测试测试用例创建验证"""
    print("=== 测试测试用例创建 Schema 验证 ===")
    
    # 测试最小有效数据
    print("1. 测试最小有效数据...")
    minimal_data = {
        "title": "测试用例标题",
        "priority": "高",
        "requirement_id": 1,
        "project_id": 1,
        "creator": "测试人员"
    }
    
    try:
        testcase = CaseCreate(**minimal_data)
        print("✓ 最小有效数据验证通过")
        print(f"   标题: {testcase.title}")
        print(f"   优先级: {testcase.priority}")
        print(f"   状态: {testcase.status}")
        print(f"   需求ID: {testcase.requirement_id}")
        print(f"   项目ID: {testcase.project_id}")
        print(f"   创建者: {testcase.creator}")
    except ValidationError as e:
        print(f"✗ 最小有效数据验证失败:")
        for error in e.errors():
            print(f"     - {error['loc'][0]}: {error['msg']}")
        return False
    
    # 测试完整有效数据
    print("\n2. 测试完整有效数据...")
    full_data = {
        "title": "完整测试用例",
        "desc": "这是一个完整的测试用例描述",
        "priority": "高",
        "status": "未开始",
        "preconditions": "前置条件",
        "postconditions": "后置条件",
        "tags": ["功能测试", "系统测试"],
        "requirement_id": 1,
        "project_id": 1,
        "creator": "张三",
        "steps": [
            {
                "description": "第一步操作",
                "expected_result": "预期结果1"
            },
            {
                "description": "第二步操作", 
                "expected_result": "预期结果2"
            }
        ]
    }
    
    try:
        testcase = CaseCreate(**full_data)
        print("✓ 完整有效数据验证通过")
        print(f"   包含 {len(testcase.steps or [])} 个测试步骤")
        print(f"   包含 {len(testcase.tags or [])} 个标签")
    except ValidationError as e:
        print(f"✗ 完整有效数据验证失败:")
        for error in e.errors():
            print(f"     - {error['loc'][0]}: {error['msg']}")
        return False
    
    # 测试用户提供的错误数据
    print("\n3. 测试用户提供的错误数据...")
    user_error_data = {
        'description': '第一步',
        'expected_result': '无'
    }
    
    try:
        testcase = CaseCreate(**user_error_data)
        print("✗ 用户错误数据应该验证失败但却成功了")
        return False
    except ValidationError as e:
        print("✓ 用户错误数据正确被拒绝:")
        for error in e.errors():
            print(f"     - {error['loc'][0]}: {error['msg']}")
    
    # 测试各种无效数据
    print("\n4. 测试各种无效数据...")
    invalid_cases = [
        {
            "name": "缺少标题",
            "data": {k: v for k, v in minimal_data.items() if k != "title"}
        },
        {
            "name": "缺少优先级",
            "data": {k: v for k, v in minimal_data.items() if k != "priority"}
        },
        {
            "name": "缺少需求ID",
            "data": {k: v for k, v in minimal_data.items() if k != "requirement_id"}
        },
        {
            "name": "缺少项目ID",
            "data": {k: v for k, v in minimal_data.items() if k != "project_id"}
        },
        {
            "name": "缺少创建者",
            "data": {k: v for k, v in minimal_data.items() if k != "creator"}
        },
        {
            "name": "无效优先级",
            "data": {**minimal_data, "priority": "无效优先级"}
        },
        {
            "name": "无效状态",
            "data": {**minimal_data, "status": "无效状态"}
        },
        {
            "name": "空标题",
            "data": {**minimal_data, "title": ""}
        }
    ]
    
    failed_validations = 0
    for case in invalid_cases:
        try:
            CaseCreate(**case["data"])
            print(f"✗ {case['name']}: 应该验证失败但却成功了")
            failed_validations += 1
        except ValidationError:
            print(f"✓ {case['name']}: 正确捕获验证错误")
    
    if failed_validations > 0:
        print(f"\n❌ 有 {failed_validations} 个无效数据测试失败")
        return False
    
    print("\n✅ 所有验证测试都通过了！")
    return True

def test_step_validation():
    """测试测试步骤验证"""
    print("\n=== 测试测试步骤验证 ===")
    
    # 测试有效步骤
    print("1. 测试有效步骤...")
    valid_step = {
        "description": "点击登录按钮",
        "expected_result": "页面跳转到主页"
    }
    
    try:
        step = TestStepCreate(**valid_step)
        print("✓ 有效步骤验证通过")
        print(f"   描述: {step.description}")
        print(f"   预期结果: {step.expected_result}")
    except ValidationError as e:
        print(f"✗ 有效步骤验证失败: {e}")
        return False
    
    # 测试无效步骤
    print("\n2. 测试无效步骤...")
    invalid_steps = [
        {
            "name": "缺少描述",
            "data": {"expected_result": "结果"}
        },
        {
            "name": "缺少预期结果",
            "data": {"description": "描述"}
        },
        {
            "name": "空描述",
            "data": {"description": "", "expected_result": "结果"}
        },
        {
            "name": "空预期结果",
            "data": {"description": "描述", "expected_result": ""}
        }
    ]
    
    for case in invalid_steps:
        try:
            TestStepCreate(**case["data"])
            print(f"✗ {case['name']}: 应该验证失败但却成功了")
        except ValidationError:
            print(f"✓ {case['name']}: 正确捕获验证错误")
    
    return True

def main():
    """主测试函数"""
    print("开始测试测试用例 Schema...\n")
    
    tests = [
        ("测试用例创建验证", test_testcase_create_validation),
        ("测试步骤验证", test_step_validation),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print(f"\n{'='*60}")
    print("测试结果总结:")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 Schema 测试通过！")
        return True
    else:
        print(f"\n❌ 还有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
