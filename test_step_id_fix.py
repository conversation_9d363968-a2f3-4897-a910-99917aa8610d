#!/usr/bin/env python3
"""
测试修复后的测试用例和步骤 Schema
"""

import sys
import os
from typing import List, Optional
from enum import Enum
from pydantic import BaseModel, Field, ValidationError

# 直接定义枚举和 Schema
class Priority(str, Enum):
    HIGH = "高"
    MEDIUM = "中"
    LOW = "低"

class Status(str, Enum):
    NOT_STARTED = "未开始"
    IN_PROGRESS = "进行中"
    PASSED = "通过"
    FAILED = "失败"
    BLOCKED = "阻塞"

class TestCaseTag(str, Enum):
    UNIT_TEST = "单元测试"
    FUNCTIONAL_TEST = "功能测试"
    INTEGRATION_TEST = "集成测试"
    SYSTEM_TEST = "系统测试"
    SMOKE_TEST = "冒烟测试"
    VERSION_VERIFICATION = "版本验证"

# 测试步骤模型
class TestStepBase(BaseModel):
    description: str = Field(..., min_length=1, description="测试步骤的描述。")
    expected_result: str = Field(..., min_length=1, description="测试步骤的预期结果。")

class TestStepCreate(TestStepBase):
    class Config:
        extra = "forbid"  # 禁止额外字段

class TestStep(TestStepBase):
    step_id: int = Field(..., description="测试步骤的唯一标识。")

    class Config:
        from_attributes = True

# 测试用例模型
class TestCaseBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200, description="测试用例的标题。")
    desc: Optional[str] = Field(None, max_length=1000, description="测试用例的详细描述。")
    priority: Priority = Field(..., description="测试用例的优先级。")
    status: Status = Field(default=Status.NOT_STARTED, description="测试用例的当前状态。")
    preconditions: Optional[str] = Field(None, description="测试用例的前置条件。")
    postconditions: Optional[str] = Field(None, description="测试用例的后置条件。")
    tags: Optional[TestCaseTag] = Field(TestCaseTag.FUNCTIONAL_TEST, description="测试用例的标签，用于分类或过滤。")
    requirement_id: int = Field(..., gt=0, description="关联需求ID。")
    project_id: int = Field(..., gt=0, description="关联项目ID。")
    creator: str = Field(..., min_length=1, max_length=100, description="测试用例的创建者姓名。")

class TestCase(TestCaseBase):
    test_case_id: int = Field(..., description="测试用例的唯一标识。", alias="id")
    created_time: Optional[str] = Field(None, description="测试用例的创建时间。")
    steps: Optional[List[TestStep]] = Field(None, description="测试步骤列表。")

    class Config:
        from_attributes = True
        populate_by_name = True

class CaseCreate(TestCaseBase):
    steps: Optional[List[TestStepCreate]] = Field(None, description="测试步骤列表。")

    class Config:
        extra = "forbid"  # 禁止额外字段

def test_user_error_data():
    """测试用户提供的错误数据"""
    print("=== 测试用户提供的错误数据 ===")
    
    # 用户提供的数据（包含 step_id 的完整测试用例）
    user_data = {
        'title': '自动化测试用例',
        'desc': '这是一个自动化测试用例',
        'priority': '高',
        'requirement_id': 1,
        'project_id': 1,
        'creator': '测试工程师',
        'steps': [
            {
                'description': '第一步操作',
                'expected_result': '成功',
                'step_id': 1  # 这里包含了 step_id，但创建时不应该包含
            }
        ]
    }
    
    print("1. 测试包含 step_id 的创建数据...")
    try:
        testcase = CaseCreate(**user_data)
        print("✗ 应该验证失败但却成功了（step_id 不应该在创建时提供）")
        return False
    except ValidationError as e:
        print("✓ 正确拒绝了包含 step_id 的创建数据:")
        for error in e.errors():
            print(f"     - {error['loc']}: {error['msg']}")
    
    # 修正的数据（移除 step_id）
    corrected_data = {
        'title': '自动化测试用例',
        'desc': '这是一个自动化测试用例',
        'priority': '高',
        'requirement_id': 1,
        'project_id': 1,
        'creator': '测试工程师',
        'steps': [
            {
                'description': '第一步操作',
                'expected_result': '成功'
                # 移除了 step_id
            }
        ]
    }
    
    print("\n2. 测试修正后的创建数据...")
    try:
        testcase = CaseCreate(**corrected_data)
        print("✓ 修正后的数据验证通过")
        print(f"   标题: {testcase.title}")
        print(f"   优先级: {testcase.priority}")
        print(f"   步骤数量: {len(testcase.steps or [])}")
        if testcase.steps:
            print(f"   第一步描述: {testcase.steps[0].description}")
            print(f"   第一步预期结果: {testcase.steps[0].expected_result}")
    except ValidationError as e:
        print(f"✗ 修正后的数据验证失败:")
        for error in e.errors():
            print(f"     - {error['loc']}: {error['msg']}")
        return False
    
    return True

def test_response_data():
    """测试响应数据（包含 step_id）"""
    print("\n=== 测试响应数据 ===")
    
    # 模拟从数据库返回的数据（包含 step_id）
    response_data = {
        'test_case_id': 1,
        'title': '登录功能测试',
        'desc': '测试用户登录功能',
        'priority': '高',
        'status': '未开始',
        'tags': '功能测试',
        'requirement_id': 1,
        'project_id': 1,
        'creator': '测试工程师',
        'created_time': '2024-01-01T10:00:00',
        'steps': [
            {
                'step_id': 1,
                'description': '输入用户名和密码',
                'expected_result': '显示登录表单'
            },
            {
                'step_id': 2,
                'description': '点击登录按钮',
                'expected_result': '成功登录并跳转'
            }
        ]
    }
    
    try:
        testcase = TestCase(**response_data)
        print("✓ 响应数据验证通过")
        print(f"   测试用例ID: {testcase.test_case_id}")
        print(f"   标题: {testcase.title}")
        print(f"   步骤数量: {len(testcase.steps or [])}")
        if testcase.steps:
            for i, step in enumerate(testcase.steps, 1):
                print(f"   步骤{i} ID: {step.step_id}")
                print(f"   步骤{i} 描述: {step.description}")
    except ValidationError as e:
        print(f"✗ 响应数据验证失败:")
        for error in e.errors():
            print(f"     - {error['loc']}: {error['msg']}")
        return False
    
    return True

def test_minimal_valid_data():
    """测试最小有效数据"""
    print("\n=== 测试最小有效数据 ===")
    
    minimal_data = {
        "title": "最小测试用例",
        "priority": "中",
        "requirement_id": 1,
        "project_id": 1,
        "creator": "测试人员"
    }
    
    try:
        testcase = CaseCreate(**minimal_data)
        print("✓ 最小有效数据验证通过")
        print(f"   标题: {testcase.title}")
        print(f"   优先级: {testcase.priority}")
        print(f"   状态: {testcase.status}")
        print(f"   标签: {testcase.tags}")
        print(f"   步骤: {testcase.steps}")
    except ValidationError as e:
        print(f"✗ 最小有效数据验证失败:")
        for error in e.errors():
            print(f"     - {error['loc']}: {error['msg']}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始测试修复后的测试用例 Schema...\n")
    
    tests = [
        ("用户错误数据", test_user_error_data),
        ("响应数据", test_response_data),
        ("最小有效数据", test_minimal_valid_data),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print(f"\n{'='*60}")
    print("测试结果总结:")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 Schema 修复成功！")
        print("\n📝 使用说明:")
        print("- 创建测试用例时，steps 中不要包含 step_id")
        print("- step_id 会在数据库中自动生成")
        print("- 查询响应中会包含完整的 step_id 信息")
        return True
    else:
        print(f"\n❌ 还有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
