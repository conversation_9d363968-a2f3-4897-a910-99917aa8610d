from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX IF EXISTS "idx_test_steps_step_id_c3105a";
        DROP INDEX IF EXISTS "idx_test_cases_test_ca_74d8a1";
        ALTER TABLE "test_cases" DROP COLUMN "test_case_id";
        ALTER TABLE "test_steps" DROP COLUMN "step_id";"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "test_cases" ADD "test_case_id" INT NOT NULL /* 测试用例的唯一标识。 */;
        ALTER TABLE "test_steps" ADD "step_id" INT NOT NULL /* 测试步骤的唯一标识。 */;
        CREATE INDEX "idx_test_cases_test_ca_74d8a1" ON "test_cases" ("test_case_id");
        CREATE INDEX "idx_test_steps_step_id_c3105a" ON "test_steps" ("step_id");"""
